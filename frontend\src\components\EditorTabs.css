.editor-tabs {
  display: flex;
  background-color: #252526;
  padding-top: 5px;
  border-bottom: 1px solid #333;
  user-select: none;
}

.editor-tab {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #2d2d2d;
  border-right: 1px solid #252526;
  cursor: pointer;
  color: #9e9e9e;
  font-size: 14px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  max-width: 200px;
}

.editor-tab:hover {
  background-color: #3e3e3e;
}

.editor-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

.tab-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
}

.tab-close {
  background: none;
  border: none;
  color: #9e9e9e;
  font-size: 18px;
  line-height: 1;
  cursor: pointer;
  padding: 0 4px;
  border-radius: 50%;
}

.tab-close:hover {
  background-color: #555;
  color: #fff;
}

.editor-tab.active .tab-close {
  color: #fff;
}