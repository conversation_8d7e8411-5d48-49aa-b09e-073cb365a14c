.app-container {
  display: flex;
  height: 100%;
}

.panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--panel-bg-color);
  border-left: 1px solid var(--border-color);
}

.panel:first-child {
  border-left: none;
}

.panel h2 {
  font-size: 1rem;
  padding: 0.5rem 1rem;
  margin: 0;
  background-color: var(--panel-header-bg-color);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
}

.file-tree-panel {
  flex: 1 1 20%;
  min-width: 200px;
}

.editor-panel {
  flex: 2 1 50%;
  min-width: 300px;
}

.chat-panel {
  flex: 1 1 30%;
  min-width: 320px;
}

.editor-content {
  height: 100%;
  overflow: auto;
  background-color: var(--editor-bg-color);
  flex-grow: 1;
}

.editor-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--placeholder-text-color);
  padding: 1rem;
}

.editor-placeholder p {
  font-style: italic;
}

.editor-wrapper {
  display: flex;
  font-family: Consol<PERSON>, "Courier New", monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  padding: 1rem;
}

.line-numbers {
  padding-right: 1rem;
  text-align: right;
  color: var(--placeholder-text-color);
  user-select: none;
}

.editor-wrapper pre {
  margin: 0;
  flex-grow: 1;
}

/* ChatPanel Styles */
.chat-panel-container {
  display: flex;
  flex-direction: column;
  background-color: var(--chat-bg-color);
  flex-grow: 1;
  /* Ensure the container can shrink and not overflow its parent panel */
  min-height: 0;
}

.chat-history,
.file-tree-container,
.editor-content,
.history-panel ul {
  scrollbar-width: thin;
  scrollbar-color: #555 transparent;
}

.message-wrapper {
  position: relative;
  margin-bottom: 0.75rem;
  display: flex; /* Use flexbox for alignment */
}

.message-wrapper:hover {
  border: 1px solid var(--border-color);
  border-radius: 1.25rem;
}

.message {
  display: flex;
  flex-direction: column;
  width: 100%; /* Make message take full width of wrapper */
}

.user-message {
  align-items: flex-end;
}

.ai-message {
  align-items: flex-start;
}

.message-bubble {
  padding: 0.5rem 1rem;
  border-radius: 1.25rem;
  max-width: 80%;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.user-message .message-bubble {
  background-color: var(--user-message-bg-color);
  color: var(--user-message-text-color);
  border-bottom-right-radius: 0.25rem;
}

.ai-message .message-bubble {
  background-color: var(--ai-message-bg-color);
  color: var(--ai-message-text-color);
  border-bottom-left-radius: 0.25rem;
}

.message-wrapper .delete-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1rem;
  padding: 0.5rem;
  display: none; /* Hidden by default */
  border-radius: 50%;
}

.message-wrapper:hover .delete-button {
  display: block; /* Show on hover */
}

.delete-button:hover {
  background-color: var(--file-node-hover-bg-color);
}

.delete-button-user {
  left: 5px; /* Position on the left for user messages */
}

.delete-button-ai {
  right: 5px; /* Position on the right for AI messages */
}

.message-actions {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column; /* Stack buttons vertically */
  gap: 5px;
  background-color: transparent; /* Make container background transparent */
  padding: 4px;
  border-radius: 8px;
}

.message-actions-user {
  left: 5px;
}

.message-actions-ai {
  right: 5px;
}

.action-button {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1rem;
}

.action-button:hover {
  color: var(--button-primary-bg-color);
}

.edit-container {
  width: 100%;
}

.edit-textarea {
  width: 100%;
  background-color: var(--input-border-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  font-family: inherit;
  font-size: inherit;
  resize: vertical;
  min-height: 60px;
}

.edit-buttons {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.edit-buttons button {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.edit-buttons button:first-child {
  background-color: var(--button-primary-bg-color);
  color: var(--button-primary-text-color);
}

.edit-buttons button:last-child {
  background-color: var(--button-danger-bg-color);
  color: var(--button-primary-text-color);
}

.apply-changes-button-container {
  margin-top: 0.5rem;
}

.apply-changes-button {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  border: 1px solid var(--apply-changes-button-border-color);
  background-color: var(--apply-changes-button-bg-color);
  color: var(--apply-changes-button-text-color);
  cursor: pointer;
  font-size: 0.8rem;
}

.apply-changes-button:hover {
  background-color: var(--apply-changes-button-hover-bg-color);
  color: var(--apply-changes-button-hover-text-color);
}

.chat-input-form {
  display: flex;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--chat-input-bg-color);
  flex-wrap: wrap;
}

.chat-input {
  flex-grow: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--input-border-color);
  border-radius: 4px;
  margin-right: 0.5rem;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  box-sizing: border-box;
}

.send-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  background-color: var(--button-primary-bg-color);
  color: var(--button-primary-text-color);
  cursor: pointer;
  font-weight: 600;
}

.send-button:disabled {
  background-color: var(--button-primary-disabled-bg-color);
  cursor: not-allowed;
}

/* FileTree Styles */
.file-tree-container {
  padding: 0.5rem;
  height: 100%;
  overflow-y: auto;
  flex-grow: 1;
}

.file-tree-status {
  padding: 1rem;
  color: var(--placeholder-text-color);
}

.file-node-container {
  padding-left: 1rem;
}

.file-node {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  cursor: pointer;
  border-radius: 4px;
}

.file-node:hover {
  background-color: var(--file-node-hover-bg-color);
}

.file-node.active {
  background-color: #3a3d41;
}

.file-icon {
  margin-right: 0.5rem;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--file-node-text-color);
}

.changes-summary {
  background-color: #2c2c2c;
  border-top: 1px solid #444;
  padding: 10px;
  margin-top: 10px;
  border-radius: 0 0 8px 8px;
}

.changes-summary strong {
  color: #a9b7c6;
  font-size: 0.9em;
}

.changes-summary ul {
  list-style-type: none;
  padding-left: 0;
  margin-top: 5px;
}

.changes-summary li {
  color: #888;
  font-size: 0.85em;
  margin-bottom: 5px;
  padding-top: 10px;
  border-top: 1px solid #3a3a3a;
}

.changes-summary li:first-child {
  border-top: none;
  padding-top: 0;
}

.change-file {
  color: #6a8759;
  font-weight: bold;
}

.chat-history,
.file-tree-container,
.editor-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  min-height: 0;
}
/* Webkit-based browsers (Chrome, Safari, Edge) */
.chat-history::-webkit-scrollbar,
.file-tree-container::-webkit-scrollbar,
.editor-content::-webkit-scrollbar,
.history-panel ul::-webkit-scrollbar,
.chat-input::-webkit-scrollbar {
  width: 8px;
}

.chat-history::-webkit-scrollbar-track,
.file-tree-container::-webkit-scrollbar-track,
.editor-content::-webkit-scrollbar-track,
.history-panel ul::-webkit-scrollbar-track,
.chat-input::-webkit-scrollbar-track {
  background: transparent;
}

.chat-history::-webkit-scrollbar-thumb,
.file-tree-container::-webkit-scrollbar-thumb,
.editor-content::-webkit-scrollbar-thumb,
.history-panel ul::-webkit-scrollbar-thumb,
.chat-input::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}


/* Card styles for token usage and files */
.token-usage-card, .files-card {
  background-color: #2c2c2c;
  border-left: 4px solid #007bff;
  padding: 8px 12px;
  margin-top: 10px;
  border-radius: 4px;
  font-size: 0.9em;
  color: #a9b7c6;
}

.token-usage-card strong, .files-card strong {
  display: block;
  margin-bottom: 5px;
  color: #a9b7c6;
}

.token-usage-card ul, .files-card ul {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}

.token-usage-card ul li, .files-card ul li {
  padding: 2px 0;
  font-family: monospace;
}

.chat-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--panel-header-bg-color);
  border-bottom: 1px solid var(--border-color);
}

.chat-panel-header h2 {
  padding: 0.5rem 1rem;
  margin: 0;
  border-bottom: none;
  flex-grow: 1;
}

.chat-header-buttons {
  display: flex;
  gap: 0.5rem;
  padding-right: 1rem;
}

.header-button {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: background-color 0.2s, border-color 0.2s;
}

.header-button:hover {
  background-color: var(--file-node-hover-bg-color);
  border-color: var(--button-primary-bg-color);
}

.new-chat-button {
  font-weight: bold;
}

.history-button {
  font-weight: bold;
}

.history-panel {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--panel-bg-color);
}

.history-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

.history-panel li {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.history-panel li:hover {
  background-color: var(--file-node-hover-bg-color);
}

.history-panel li:last-child {
  border-bottom: none;
}

.history-panel li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.history-item-title {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-buttons {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.history-item-buttons button {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;
  border-radius: 4px;
}

.history-item-buttons button:hover {
  background-color: var(--file-node-hover-bg-color);
}

.stop-button {
  background-color: var(--button-danger-bg-color, #c72c41);
}

.stop-button:hover {
  background-color: var(--button-danger-hover-bg-color, #a52436);
}
