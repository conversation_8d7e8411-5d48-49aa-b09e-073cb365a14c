# AIStudioBuildCopy

这是一个基于 Web 的交互式集成开发环境（IDE），它利用 Google Gemini AI 的强大功能来辅助代码的编写、理解和修改。用户可以通过自然语言与 AI 对话，AI 能够读取和修改指定项目目录中的文件，从而实现智能化的代码生成与重构。

## ✨ 核心功能

- **三栏式界面**:
  - **文件树**: 实时展示项目目录结构。
  - **代码编辑器**: 查看和浏览选定文件的内容。
  - **交互式聊天**: 与 AI 助手进行对话，下达开发指令。
- **项目上下文感知**: AI 能够完整读取 `project/` 目录下的所有文件，基于完整的项目上下文进行思考和编码。
- **AI 驱动的代码修改**: 集成 Google Gemini API，根据用户指令智能生成代码，并自动执行文件创建、修改等操作。
- **实时文件同步**: 后端服务会监控文件系统的变更，并实时将更新推送到前端，确保文件树和编辑器内容始终最新。
- **对话历史管理**: 自动保存每一轮对话，方便回顾和继续之前的任务。
- **一键备份与恢复**: 在 AI 应用修改之前，会自动创建项目快照，支持一键恢复到修改前的状态。

## 🏛️ 技术架构

- **后端**: 使用 **Node.js** 和 **Express** 构建，负责文件操作、与 AI 服务通信以及通过 Server-Sent Events (SSE) 向前端推送实时更新。
- **前端**: 使用 **React** 和 **Vite** 构建，提供响应式的用户界面。
- **AI 模型**: **Google Gemini**。

## 🚀 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/Lianues/AIStudioBuildCopy
cd AIStudioBuildCopy
```

### 2. 安装依赖

本项目包含一个主后端和位于 `frontend` 目录下的前端应用，需要分别安装它们的依赖。

```bash
# 安装后端依赖
npm install

# 安装前端依赖
npm install --prefix frontend
```

### 3. 配置环境变量

在项目根目录下创建一个名为 `.env` 的文件，并填入您的 Google Gemini API 密钥：

```
GEMINI_API_KEY=YOUR_API_KEY_HERE
```

> **注意**: `.env` 文件已被添加到 `.gitignore` 中，以防止您的密钥被意外提交。

### 4. 准备您的项目

将您希望 AI 操作的代码文件放入项目根目录下的 `project/` 文件夹中。如果该文件夹不存在，请手动创建它。
配置文件 `project/.aiignore` 来设置用于给ai提交的项目文件，同时也是不显示在前端的文件

目前只要是显示在前端的文件，会全部发送给ai，所以如果有大量配置文件（比如node_model），就会导致发送太多卡死

这个里面屏蔽和.gitignore是一样的
你在前端里面看不到这个文件或者文件夹了，就说明屏蔽成功了


## 🔧 如何运行

执行以下命令来同时启动后端服务和前端开发服务器：

```bash
npm run dev
```

该命令会：
1.  启动后端 Express 服务器（默认在 `http://localhost:3001`）。
2.  启动前端 Vite 开发服务器（通常在 `http://localhost:5173`）。

应用启动后，请在浏览器中打开前端应用的地址。

## 📝 如何使用

1.  在浏览器中打开应用。
2.  左侧的文件树会显示您放在 `project/` 目录下的所有文件。
3.  点击文件可以在中间的编辑器中查看其内容。
4.  在右侧的聊天面板中，输入您的开发需求，例如：“请在 `index.js` 中添加一个 `add` 函数，用于计算两个数字的和”。
5.  AI 会分析您的需求和项目文件，然后在聊天中给出它的思考过程和计划执行的代码修改。
6.  修改被应用后，您会看到文件树和编辑器中的内容实时更新。
