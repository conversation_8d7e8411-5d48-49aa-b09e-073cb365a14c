{"langCode": "zh", "history": "历史记录", "settings": "设置", "github": "在 GitHub 上查看", "appTagline": "您的人工智能深度研究助手。", "modeBalanced": "平衡模式", "modeBalancedDesc": "质量与速度的最佳组合。", "modeDeepDive": "深度研究", "modeDeepDiveDesc": "使用最强模型以获得最高质量。", "modeFast": "快速模式", "modeFastDesc": "使用高效模型快速得出结果。", "modeUltraFast": "超快模式", "modeUltraFastDesc": "闪电般快速的结果，适用于快速检查。", "advancedSearch": "高级：引导初始搜索", "guidedSearchPlaceholder": "初始搜索主题，每行一个...", "mainQueryPlaceholder": "人工智能在医疗保健领域的未来是什么？（您也可以附加文件）", "attachFile": "附加文件", "removeFile": "移除文件", "startResearch": "开始研究", "stopResearch": "停止研究", "continueResearch": "继续研究", "generateReport": "生成报告", "generatingReport": "正在生成报告...", "toggleResearchLog": "{{action}}研究日志", "show": "显示", "hide": "隐藏", "startNewResearch": "开始新研究", "footerPoweredBy": "由 Gemini 驱动。", "refiningRequest": "正在优化您的请求", "refiningRequestDesc": "为了获得最佳结果，AI 可能会问几个澄清性问题。", "clarificationPlaceholder": "您的回答...", "clarificationPlaceholderInitial": "请提供您希望优化的研究主题。", "sendAnswer": "发送回答", "skipAndStart": "跳过并开始研究", "thinking": "思考中...", "waiting": "等待中...", "researchHistory": "研究历史", "close": "关闭", "searchHistory": "搜索历史...", "noMatchingHistory": "未找到匹配的历史记录。", "tryDifferentSearch": "请尝试其他搜索词。", "historyAppearsHere": "完成的研究将在此处显示。", "editTitle": "编辑标题", "delete": "删除", "load": "加载", "clearAllHistory": "清除所有历史记录", "researching": "研究中...", "researchComplete": "研究完成", "searchingFor": "正在搜索：", "readAndSynthesized": "阅读与整合", "sources": "个来源", "thought": "想法", "outline": "报告大纲", "agentAlpha": "智能体 Alpha", "agentBeta": "智能体 Beta", "visualizingReport": "智能体正在生成您的可视化报告...", "noVisualReport": "尚未生成可视化报告。", "apiKey": "API 密钥", "parameters": "参数", "models": "模型", "restoreDefaults": "恢复默认值", "dark": "深色", "light": "浅色", "citations": "引用", "untitledSource": "无标题来源", "finalReport": "最终报告", "copied": "已复制！", "failed": "失败！", "copy": "复制", "reportOnly": "仅报告", "reportAndCitations": "报告与引用", "regenerating": "重新生成中", "success": "成功！", "regenerateReport": "重新生成报告", "visualizing": "可视化中...", "visualize": "可视化", "researchSummary": "研究摘要", "researchTime": "研究用时", "sourcesFound": "找到的来源", "searchCycles": "搜索周期", "translate": "翻译", "addEmojis": "添加表情", "addFinalPolish": "最终润色", "readingLevel": "阅读水平", "adjustLength": "调整长度", "customEdit": "自定义编辑", "adjustLengthTitle": "调整长度", "muchShorter": "大幅缩短", "shorter": "缩短", "longer": "加长", "muchLonger": "大幅加长", "readingLevelTitle": "阅读水平", "kindergarten": "幼儿园", "middleSchool": "初中", "highSchool": "高中", "college": "大学", "graduateSchool": "研究生", "addEmojisTitle": "添加表情", "emojisToWords": "添加到词语", "emojisToSections": "添加到章节", "emojisToLists": "添加到列表", "emojisRemoveAll": "全部移除", "customEditTitle": "自定义编辑", "customEditPlaceholder": "例如，用更随意的语气重写，并使用附加文件作为上下文。", "attach": "附加", "apply": "应用", "translateReportTitle": "翻译报告", "language": "语言", "languagePlaceholder": "例如，中文、马来语", "style": "风格", "colloquial": "口语化", "literal": "字面", "translating": "翻译中...", "geminiApiKeys": "Gemini API 密钥", "apiKeysConfiguredByHost": "API 密钥由应用主机配置。", "apiKeysPlaceholder": "输入您的 Gemini API 密钥，每行一个。", "apiBaseUrl": "API 基地址 (可选)", "apiBaseUrlDesc": "仅在需要使用代理或不同 API 端点时更改此项。", "apiBaseUrlPlaceholder": "例如, https://generativelanguage.googleapis.com", "modelConfig": "模型配置", "refreshModelList": "刷新模型列表", "loading": "加载中...", "modelConfigDesc": "为每个智能体覆盖默认模型。选择“默认”以使用当前研究模式指定的模型。", "defaultModel": "默认", "researchParams": "研究参数", "minCycles": "最少研究周期", "minCyclesHelp": "完成前的最少周期。", "maxCycles": "最多研究周期", "maxCyclesHelp": "研究迭代的硬性限制。", "maxDebateRounds": "最多辩论回合", "maxDebateRoundsHelp": "智能体规划对话的长度。", "uncapped": "无上限", "feedback": "反馈", "giveFeedback": "提供反馈", "cancel": "取消", "feedbackPlaceholder": "例如，“把图表变成蓝色”或“添加一个关于财务影响的部分”。", "generateNewVersion": "生成新版本", "feedbackSuccess": "成功！正在重新生成...", "feedbackError": "更新失败，请重试。", "visualReport": "可视化报告", "regenerate": "重新生成", "download": "下载", "changeLanguage": "切换语言", "apiKeyRequiredTitle": "需要 API 密钥", "apiKeyRequiredMessage": "开始研究前，请在设置中配置您的 Gemini API 密钥。", "emptyQueryTitle": "查询为空", "emptyQueryMessage": "无法使用空查询开始研究。", "initialSearchFailedTitle": "初始搜索失败", "allApiKeysFailedTitle": "所有 API 密钥均失败", "allApiKeysFailedMessage": "您可以重试该操作或在设置中检查您的密钥。", "researchStoppedTitle": "研究已停止", "researchStoppedMessage": "研究过程已被用户取消。", "researchFailedTitle": "研究失败", "clarificationFailedTitle": "澄清失败", "apiKeysFailedMessage": "所有 API 密钥均失败。您可以重试该操作。", "clarifiedContextFailed": "澄清过程失败。将按原始查询继续。", "generatingOutlineTitle": "正在生成大纲", "generatingOutlineMessage": "正在为最终报告创建结构。", "reportGeneratedTitle": "报告已生成", "reportGeneratedMessage": "已根据目前完成的研究生成报告。", "synthesisFailedTitle": "整合失败", "synthesisFailedMessage": "整合失败。请在设置中检查您的密钥。", "visualizationFailedTitle": "可视化失败", "regeneratingOutlineTitle": "正在重新生成大纲", "regeneratingOutlineMessage": "在重新生成前重构报告结构。", "reportRegeneratedTitle": "报告已重新生成", "reportRegeneratedMessage": "已生成新版本的报告。", "regenerationFailedTitle": "重新生成失败", "reportUpdatedTitle": "报告已更新", "reportUpdatedMessage": "报告已成功重写。", "rewriteFailedTitle": "重写失败", "historyItemRemovedTitle": "历史项已移除", "historyItemRemovedMessage": "所选项已从您的研究历史中删除。", "historyClearedTitle": "历史已清除", "historyClearedMessage": "所有项都已从您的研究历史中移除。", "translationCompleteTitle": "翻译完成", "translationCompleteMessage": "报告已翻译并另存为新版本。", "translationFailedTitle": "翻译失败", "defaultsLoadedTitle": "已加载默认值", "defaultsLoadedMessage": "设置已重置为默认值并保存。", "roles": "角色", "selectRole": "选择角色", "defaultRole": "默认", "defaultRoleDesc": "标准研究智能体。", "builtIn": "内置", "custom": "自定义", "manageRoles": "管理角色", "editRole": "编辑角色", "createNewRole": "创建新角色", "edit": "编辑", "roleNamePlaceholder": "角色名称...", "rolePromptPlaceholder": "描述此角色的身份和指令。例如，'扮演一个持怀疑态度的财务分析师。关注风险和财务可行性...'", "generateNameEmoji": "生成", "refinePrompt": "优化", "creativePrompt": "创意", "attachContextFile": "附加背景文件", "saveRole": "保存角色", "confirmDeleteRole": "您确定要删除此角色吗？", "roleAI": "角色AI"}