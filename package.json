{"name": "my-cli-app", "version": "1.0.0", "description": "A simple Node.js CLI application.", "main": "dist/index.js", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "dev:backend": "tsx watch src/server.ts", "dev:frontend": "wait-on http://localhost:3001 && npm run dev --prefix frontend", "dev": "run-p dev:*"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "@google/genai": "^0.12.0", "@types/string-width": "^2.0.0", "chokidar": "^4.0.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "express-sse": "^1.0.0", "fast-xml-parser": "^5.2.5", "https-proxy-agent": "^7.0.6", "ignore": "^7.0.5", "minimatch": "^9.0.5", "openai": "^5.8.2", "string-width": "^7.2.0"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__parser": "^7.0.0", "@types/babel__traverse": "^7.20.7", "@types/cors": "^2.8.19", "@types/detect-port": "^1.3.5", "@types/dotenv": "^8.2.0", "@types/express": "^5.0.3", "@types/inquirer": "^8.2.1", "@types/kill-port": "^2.0.3", "@types/minimatch": "^5.1.0", "@types/node": "^18.11.9", "@types/uuid": "^10.0.0", "detect-port": "^2.1.0", "kill-port": "^2.0.1", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "ts-node": "^10.9.1", "tsx": "^4.20.3", "typescript": "^4.8.4", "wait-on": "^7.2.0"}}