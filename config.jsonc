{
  // API提供商, 可选 "gemini" 或 "openai"
  "apiProvider": "openai",
  // 是否显示本轮对话的token消耗提示
  "displayTokenConsumption": {
    // 是否启用token消耗提示功能
    "enabled": true,
    // 显示哪些类型的token消耗：
    // "input": 输入token
    // "output": 输出token
    // "total": 总token
    // "thoughts": 思考过程的token
    "displayTypes": ["input", "output", "total", "thoughts"]
  },
  // 在调用AI时附带的历史对话上下文的最大条数
  // -1: 附带所有历史记录
  // 0: 不附带历史记录，只发送用户当前请求
  // >0: 附带指定条数的历史记录
  "maxContextHistoryTurns": -1,
  // 是否启用代码上下文优化功能
  // 启用后，如果检测到用户提交的代码与历史记录中的代码一致，将精简历史记录以减少token消耗
  "optimizeCodeContext": true,
  // 控制是否启用流式响应，以实时接收AI返回的内容
  "enableStreaming": true,
  // 代码修改策略
  // "full": AI将返回完整的、被修改过的文件内容进行全文替换（传统方式）。
  // "block": AI将只返回被修改的代码块，由后端通过AST进行精准替换（实验性）。
  "codeChangeStrategy": "block",
  // AI模型参数配置
  "modelParameters": {
    // 要使用的AI模型, 例如 "gemini-1.5-flash", "gemini-2.5-flash" 等
    "model": "gemini-2.5-flash",
    // 生成文本的温度，值越高，创造性越强 (Gemini 范围: 0.0 - 2.0)
    "temperature": 0.1,
    // Top-p (nucleus) aampling 的参数 (Gemini 范围: 0.0 - 1.0)
    "topP": 0.95,
    // Top-k sampling 的参数
    "topK": 40,
    // 不同代码修改策略对应的系统提示词
    "prompts": {
      // “全文替换”策略使用的提示词
      "full": "SystemPrompt/ai的TS系统提示词.md",
      // “块级替换”策略使用的提示词
      "block": "SystemPrompt/ai_block_level_ts_prompt.md"
    }
  },
  // OpenAI API 参数配置
  "openaiParameters": {
    // OpenAI兼容API的基础URL
    "baseURL": "https://generativelanguage.googleapis.com/v1beta/openai/",
    // 要使用的AI模型, 例如 "gpt-4-turbo", "gpt-3.5-turbo" 等
    "model": "gemini-2.5-flash",
    // 生成文本的温度
    "temperature": 0.1,
    // Top-p (nucleus) sampling 的参数
    "topP": 0.95,
    // 不同代码修改策略对应的系统提示词
    "prompts": {
      // “全文替换”策略使用的提示词
      "full": "SystemPrompt/ai的TS系统提示词.md",
      // “块级替换”策略使用的提示词
      "block": "SystemPrompt/ai_block_level_ts_prompt.md"
    }
  }
}